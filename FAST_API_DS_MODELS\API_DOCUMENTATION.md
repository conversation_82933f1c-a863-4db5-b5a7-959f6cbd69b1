# Space Optimization API Documentation

## Overview
This FastAPI application provides endpoints for space optimization data retrieval and pipeline execution.

## New Data Retrieval APIs

### 1. fetchOptimizationData

**Endpoint:** `GET /fetchOptimizationData`

**Description:** Retrieves specific optimization metrics from the `adjusted_data_hb` table with a calculated metric change percentage.

**Query Parameters:**
- `grp_nm` (optional): Filter by Group Name
- `dpt_nm` (optional): Filter by Department Name  
- `clss_nm` (optional): Filter by Class Name
- `sub_clss_nm` (optional): Filter by Sub Class Name
- `loc_cd` (optional): Filter by Location Code

**Returns:**
- `GRP_NM`: Group Name
- `DPT_NM`: Department Name
- `CLSS_NM`: Class Name
- `SUB_CLSS_NM`: Sub Class Name
- `LOC_CD`: Location Code
- `current_lm`: Current Linear Meters
- `MIN_LM`: Minimum Linear Meters
- `max_sat_lm`: Maximum Saturation Linear Meters
- `optimized_lm`: Optimized Linear Meters
- `lm_delta`: Linear Meters Delta (change)
- `space_change_percent`: Space Change Percentage
- `new_metric`: New Metric Value
- `NET_SLS_AMT_sum_reference_month`: Net Sales Amount Sum for Reference Month
- `Final action`: Final Action (note the space in column name)
- `optimized_no_of_options`: Optimized Number of Options
- `optimized_qty`: Optimized Quantity
- `metric_change_percent`: **Calculated field** using formula: `((new_metric - NET_SLS_AMT_sum_reference_month) / NET_SLS_AMT_sum_reference_month) * 100`

**Example Request:**
```
GET /fetchOptimizationData?grp_nm=HOMEWARE&loc_cd=29072
```

**Example Response:**
```json
{
  "status": "success",
  "message": "Retrieved 25 optimization records",
  "data": [
    {
      "GRP_NM": "HOMEWARE",
      "DPT_NM": "KITCHEN",
      "CLSS_NM": "COOKWARE",
      "SUB_CLSS_NM": "POTS & PANS",
      "LOC_CD": "29072",
      "current_lm": 12.5,
      "MIN_LM": 8.0,
      "max_sat_lm": 18.0,
      "optimized_lm": 14.2,
      "lm_delta": 1.7,
      "space_change_percent": 13.6,
      "new_metric": 15420.50,
      "NET_SLS_AMT_sum_reference_month": 14200.00,
      "Final action": "Increase",
      "optimized_no_of_options": 28,
      "optimized_qty": 112,
      "metric_change_percent": 8.59
    }
  ],
  "total_records": 25
}
```

### 2. fetchPerformanceData

**Endpoint:** `GET /fetchPerformanceData`

**Description:** Combines data from `adjusted_data_hb` and `overall_performance_data_hb` tables using a LEFT JOIN. Returns all records from adjusted_data_hb with NULLs (displayed as '-') for any missing data from overall_performance_data_hb.

**Query Parameters:**
- `grp_nm` (optional): Filter by Group Name
- `dpt_nm` (optional): Filter by Department Name
- `clss_nm` (optional): Filter by Class Name
- `sub_clss_nm` (optional): Filter by Sub Class Name
- `loc_cd` (optional): Filter by Location Code

**Join Condition:** Based on common fields: `GRP_NM`, `DPT_NM`, `CLSS_NM`, `SUB_CLSS_NM`, and `LOC_CD`

**Returns:**

From `adjusted_data_hb`:
- `GRP_NM`, `DPT_NM`, `CLSS_NM`, `SUB_CLSS_NM`, `LOC_CD`
- `Performance`
- `GMV_PER_LM`

From `overall_performance_data_hb`:
- `total_lm_avg`
- `UNITS_PER_INV`
- `CUST_PEN`
- `COVER`
- `MARGIN_PERC`
- `ASP`
- `perf_bucket`
- `lm_bucket`
- `action`

**Calculated Fields:**
- `combined_bucket`: Concatenation of `perf_bucket` and `lm_bucket` with no separator (e.g., "HM", "LL", "MH")

**NULL Handling:** All NULL values are displayed as '-' instead of NULL.

**Example Request:**
```
GET /fetchPerformanceData?grp_nm=HOMEWARE&loc_cd=29072
```

**Example Response:**
```json
{
  "status": "success",
  "message": "Retrieved 30 performance records",
  "data": [
    {
      "GRP_NM": "HOMEWARE",
      "DPT_NM": "KITCHEN",
      "CLSS_NM": "COOKWARE",
      "SUB_CLSS_NM": "POTS & PANS",
      "LOC_CD": "29072",
      "Performance": 0.75,
      "GMV_PER_LM": 1250.30,
      "total_lm_avg": 12.5,
      "UNITS_PER_INV": 2.3,
      "CUST_PEN": 0.45,
      "COVER": 45,
      "MARGIN_PERC": 0.35,
      "ASP": 125.50,
      "perf_bucket": "H",
      "lm_bucket": "M",
      "action": "increase",
      "combined_bucket": "HM"
    }
  ],
  "total_records": 30,
  "join_info": {
    "adjusted_records": 30,
    "performance_records": 28,
    "joined_records": 30
  }
}
```

## Existing APIs

### Configuration Management
- `GET /get-config`: Fetch current configuration
- `GET /`: API information and help

### Pipeline Execution
- `POST /run-optimizer`: Run the full optimization pipeline
- `POST /run/{module_name}`: Run a specific module (datapreparation, saturation, optimization)

## Error Handling

All APIs return consistent error responses:

```json
{
  "status": "error",
  "message": "Error description"
}
```

Common HTTP status codes:
- `200`: Success
- `404`: No data found with specified filters
- `500`: Internal server error

## Testing

Use the provided test script to verify API functionality:

```bash
python test_new_apis.py
```

Make sure the FastAPI server is running on `http://localhost:8000` before running tests.

## Database Configuration

The APIs use the database configuration from `shared_config.json`:
- Database Type: MySQL
- Tables: `adjusted_data_hb`, `overall_performance_data_hb`
- Connection details are configured in the shared configuration file
