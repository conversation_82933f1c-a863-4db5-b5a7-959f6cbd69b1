"""
Startup script for the Space Optimization FastAPI server.
This script starts the FastAPI server with appropriate configuration.
"""

import uvicorn
import os
import sys
from pathlib import Path

def start_server():
    """Start the FastAPI server."""
    
    # Get the directory where this script is located
    current_dir = Path(__file__).parent
    
    # Change to the FastAPI directory
    os.chdir(current_dir)
    
    print("Starting Space Optimization FastAPI Server...")
    print(f"Working directory: {current_dir}")
    print("Server will be available at: http://localhost:8000")
    print("API Documentation will be available at: http://localhost:8000/docs")
    print("Alternative API docs at: http://localhost:8000/redoc")
    print("\nPress Ctrl+C to stop the server")
    print("-" * 60)
    
    try:
        # Start the server
        uvicorn.run(
            "app:app",
            host="0.0.0.0",
            port=8000,
            reload=True,  # Enable auto-reload for development
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except Exception as e:
        print(f"Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    start_server()
