"""
Test script for the new fetchOptimizationData and fetchPerformanceData APIs.
This script demonstrates how to call the APIs and handle the responses.
"""

import requests
import json
from typing import Optional

# Base URL for the FastAPI application
BASE_URL = "http://localhost:8000"

def test_fetch_optimization_data(
    grp_nm: Optional[str] = None,
    dpt_nm: Optional[str] = None,
    clss_nm: Optional[str] = None,
    sub_clss_nm: Optional[str] = None,
    loc_cd: Optional[str] = None
):
    """
    Test the fetchOptimizationData API endpoint.
    """
    print("=" * 60)
    print("Testing fetchOptimizationData API")
    print("=" * 60)
    
    # Build query parameters
    params = {}
    if grp_nm:
        params["grp_nm"] = grp_nm
    if dpt_nm:
        params["dpt_nm"] = dpt_nm
    if clss_nm:
        params["clss_nm"] = clss_nm
    if sub_clss_nm:
        params["sub_clss_nm"] = sub_clss_nm
    if loc_cd:
        params["loc_cd"] = loc_cd
    
    try:
        response = requests.get(f"{BASE_URL}/fetchOptimizationData", params=params)
        
        print(f"Status Code: {response.status_code}")
        print(f"Query Parameters: {params}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Status: {data['status']}")
            print(f"Message: {data['message']}")
            print(f"Total Records: {data['total_records']}")
            
            if data['data'] and len(data['data']) > 0:
                print("\nSample Record (first record):")
                sample_record = data['data'][0]
                for key, value in sample_record.items():
                    print(f"  {key}: {value}")
                
                print(f"\nColumns returned: {list(sample_record.keys())}")
            else:
                print("No data records returned.")
        else:
            print(f"Error: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
    
    print()

def test_fetch_performance_data(
    grp_nm: Optional[str] = None,
    dpt_nm: Optional[str] = None,
    clss_nm: Optional[str] = None,
    sub_clss_nm: Optional[str] = None,
    loc_cd: Optional[str] = None
):
    """
    Test the fetchPerformanceData API endpoint.
    """
    print("=" * 60)
    print("Testing fetchPerformanceData API")
    print("=" * 60)
    
    # Build query parameters
    params = {}
    if grp_nm:
        params["grp_nm"] = grp_nm
    if dpt_nm:
        params["dpt_nm"] = dpt_nm
    if clss_nm:
        params["clss_nm"] = clss_nm
    if sub_clss_nm:
        params["sub_clss_nm"] = sub_clss_nm
    if loc_cd:
        params["loc_cd"] = loc_cd
    
    try:
        response = requests.get(f"{BASE_URL}/fetchPerformanceData", params=params)
        
        print(f"Status Code: {response.status_code}")
        print(f"Query Parameters: {params}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Status: {data['status']}")
            print(f"Message: {data['message']}")
            print(f"Total Records: {data['total_records']}")
            
            if 'join_info' in data:
                join_info = data['join_info']
                print(f"Join Info:")
                print(f"  Adjusted Records: {join_info['adjusted_records']}")
                print(f"  Performance Records: {join_info['performance_records']}")
                print(f"  Joined Records: {join_info['joined_records']}")
            
            if data['data'] and len(data['data']) > 0:
                print("\nSample Record (first record):")
                sample_record = data['data'][0]
                for key, value in sample_record.items():
                    print(f"  {key}: {value}")
                
                print(f"\nColumns returned: {list(sample_record.keys())}")
            else:
                print("No data records returned.")
        else:
            print(f"Error: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
    
    print()

def test_api_info():
    """
    Test the root endpoint to see API information.
    """
    print("=" * 60)
    print("Testing API Information Endpoint")
    print("=" * 60)
    
    try:
        response = requests.get(f"{BASE_URL}/")
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(json.dumps(data, indent=2))
        else:
            print(f"Error: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
    
    print()

if __name__ == "__main__":
    print("FastAPI New APIs Test Script")
    print("Make sure the FastAPI server is running on http://localhost:8000")
    print()
    
    # Test API information
    test_api_info()
    
    # Test fetchOptimizationData without filters
    test_fetch_optimization_data()
    
    # Test fetchOptimizationData with filters (example)
    test_fetch_optimization_data(grp_nm="HOMEWARE", loc_cd="29072")
    
    # Test fetchPerformanceData without filters
    test_fetch_performance_data()
    
    # Test fetchPerformanceData with filters (example)
    test_fetch_performance_data(grp_nm="HOMEWARE", loc_cd="29072")
    
    print("Test completed!")
