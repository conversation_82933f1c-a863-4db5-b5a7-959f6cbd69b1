from fastapi import FastAPI, Body, HTTPException, Query
from fastapi.responses import JSONResponse
import subprocess
import sys
from typing import Dict, Any, List, Optional
from pathlib import Path
from pydantic import BaseModel
import pandas as pd
import numpy as np
from shared_config import get_config, update_config
from db_writer import DBWriter

base_dir = Path(__file__).resolve().parent
script_path = base_dir / "run_all_modules.py"
app = FastAPI()

@app.get("/")
def root():
    return {
        "message": "Welcome to the Optimizer API",
        "available_endpoints": {
            "GET /": "This help message",
            "GET /get-config": "Fetch current configuration",
            "POST /run-optimizer": "Run the full optimization pipeline",
            "POST /run/{module_name}": "Run a specific module (datapreparation, saturation, optimization)",
            "GET /fetchOptimizationData": "Fetch optimization metrics from adjusted_data_hb table",
            "GET /fetchPerformanceData": "Fetch combined performance data from adjusted_data_hb and overall_performance_data_hb tables"
        },
        "data_endpoints_info": {
            "fetchOptimizationData": {
                "description": "Returns optimization metrics with calculated metric_change_percent",
                "query_parameters": ["grp_nm", "dpt_nm", "clss_nm", "sub_clss_nm", "loc_cd"],
                "returns": "GRP_NM, DPT_NM, CLSS_NM, SUB_CLSS_NM, LOC_CD, current_lm, MIN_LM, max_sat_lm, optimized_lm, lm_delta, space_change_percent, new_metric, NET_SLS_AMT_sum_reference_month, Final action, optimized_no_of_options, optimized_qty, metric_change_percent"
            },
            "fetchPerformanceData": {
                "description": "Returns combined data using LEFT JOIN between adjusted_data_hb and overall_performance_data_hb",
                "query_parameters": ["grp_nm", "dpt_nm", "clss_nm", "sub_clss_nm", "loc_cd"],
                "returns": "All fields from both tables with combined_bucket calculated field"
            }
        }
    }

@app.get("/get-config")
async def fetch_config():
    try:
        return get_config()
    except Exception as e:
        return JSONResponse(status_code=500, content={"status": "error", "message": str(e)})

@app.post("/run-optimizer")
async def run_optimizer(overrides: Dict[str, Any] = Body(...)):
    try:
        if overrides:
            update_config(overrides)

        result = subprocess.run([sys.executable, script_path], capture_output=True, text=True)
        if result.returncode != 0:
            return JSONResponse(status_code=500, content={
                "status": "error",
                "message": "Pipeline execution failed",
                "stderr": result.stderr
            })

        return {
            "status": "success",
            "message": "Pipeline executed successfully",
            "stdout": result.stdout
        }

    except Exception as e:
        return JSONResponse(status_code=500, content={"status": "error", "message": str(e)})

@app.post("/run/{module_name}")
async def run_single_module(module_name: str, overrides: Dict[str, Any] = Body(default={})):
    try:
        valid_modules = ["datapreparation", "saturation", "optimization"]

        if module_name not in valid_modules:
            return JSONResponse(status_code=400, content={
                "status": "error",
                "message": f"Invalid module name. Choose from: {valid_modules}"
            })

        if overrides:
            update_config(overrides)

        result = subprocess.run(
            [sys.executable, "run_all_modules.py", module_name],
            capture_output=True,
            text=True
        )

        if result.returncode != 0:
            return JSONResponse(status_code=500, content={
                "status": "error",
                "message": f"{module_name} module failed.",
                "stderr": result.stderr
            })

        return {
            "status": "success",
            "message": f"{module_name} module executed successfully",
            "stdout": result.stdout
        }

    except Exception as e:
        return JSONResponse(status_code=500, content={"status": "error", "message": str(e)})


@app.get("/fetchOptimizationData")
async def fetch_optimization_data(
    grp_nm: Optional[str] = Query(None, description="Filter by Group Name"),
    dpt_nm: Optional[str] = Query(None, description="Filter by Department Name"),
    clss_nm: Optional[str] = Query(None, description="Filter by Class Name"),
    sub_clss_nm: Optional[str] = Query(None, description="Filter by Sub Class Name"),
    loc_cd: Optional[str] = Query(None, description="Filter by Location Code")
):
    """
    Fetch optimization metrics from adjusted_data_hb table.
    Returns specific columns with calculated metric_change_percent.
    """
    try:
        cfg = get_config()
        db_writer = DBWriter(cfg)

        # Build filters based on query parameters
        filters = {}
        if grp_nm:
            filters["GRP_NM"] = grp_nm
        if dpt_nm:
            filters["DPT_NM"] = dpt_nm
        if clss_nm:
            filters["CLSS_NM"] = clss_nm
        if sub_clss_nm:
            filters["SUB_CLSS_NM"] = sub_clss_nm
        if loc_cd:
            filters["LOC_CD"] = loc_cd

        # Read data from adjusted_data_hb table
        table_name = cfg["DATABASE_CONFIG"]["RESULT_TABLES"]["ADJUSTED_DATA"]
        df = db_writer.read(
            table_name=table_name,
            filters=filters if filters else None,
            latest_only=True
        )

        if df.empty:
            return JSONResponse(
                status_code=404,
                content={"status": "error", "message": "No data found with the specified filters"}
            )

        # Select required columns
        required_columns = [
            'GRP_NM', 'DPT_NM', 'CLSS_NM', 'SUB_CLSS_NM', 'LOC_CD',
            'current_lm', 'MIN_LM', 'max_sat_lm', 'optimized_lm', 'lm_delta',
            'space_change_precent', 'new_metric', 'NET_SLS_AMT_sum_reference_month',
            'Final_Action', 'optimized_no_of_options', 'optimized_qty'
        ]

        # Check which columns exist in the dataframe
        available_columns = [col for col in required_columns if col in df.columns]
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            print(f"Warning: Missing columns in adjusted_data_hb: {missing_columns}")

        # Select available columns
        result_df = df[available_columns].copy()

        # Calculate metric_change_percent
        if 'new_metric' in result_df.columns and 'NET_SLS_AMT_sum_reference_month' in result_df.columns:
            result_df['metric_change_percent'] = (
                (result_df['new_metric'] - result_df['NET_SLS_AMT_sum_reference_month']) /
                result_df['NET_SLS_AMT_sum_reference_month'] * 100
            ).round(2)
        else:
            result_df['metric_change_percent'] = None

        # Handle column name mapping (space_change_precent -> space_change_percent)
        if 'space_change_precent' in result_df.columns:
            result_df = result_df.rename(columns={'space_change_precent': 'space_change_percent'})

        # Handle Final_Action column name (note the space in "Final action")
        if 'Final_Action' in result_df.columns:
            result_df = result_df.rename(columns={'Final_Action': 'Final action'})

        # Replace NaN values with None for JSON serialization
        result_df = result_df.where(pd.notnull(result_df), None)

        # Convert to list of dictionaries
        result_data = result_df.to_dict('records')

        return {
            "status": "success",
            "message": f"Retrieved {len(result_data)} optimization records",
            "data": result_data,
            "total_records": len(result_data)
        }

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": f"Failed to fetch optimization data: {str(e)}"}
        )


@app.get("/fetchPerformanceData")
async def fetch_performance_data(
    grp_nm: Optional[str] = Query(None, description="Filter by Group Name"),
    dpt_nm: Optional[str] = Query(None, description="Filter by Department Name"),
    clss_nm: Optional[str] = Query(None, description="Filter by Class Name"),
    sub_clss_nm: Optional[str] = Query(None, description="Filter by Sub Class Name"),
    loc_cd: Optional[str] = Query(None, description="Filter by Location Code")
):
    """
    Fetch combined performance data from adjusted_data_hb and overall_performance_data_hb tables.
    Uses LEFT JOIN to return all records from adjusted_data_hb with NULLs for missing data.
    """
    try:
        cfg = get_config()
        db_writer = DBWriter(cfg)

        # Build filters based on query parameters
        filters = {}
        if grp_nm:
            filters["GRP_NM"] = grp_nm
        if dpt_nm:
            filters["DPT_NM"] = dpt_nm
        if clss_nm:
            filters["CLSS_NM"] = clss_nm
        if sub_clss_nm:
            filters["SUB_CLSS_NM"] = sub_clss_nm
        if loc_cd:
            filters["LOC_CD"] = loc_cd

        # Get table names from config
        adjusted_table = cfg["DATABASE_CONFIG"]["RESULT_TABLES"]["ADJUSTED_DATA"]
        performance_table = cfg["DATABASE_CONFIG"]["RESULT_TABLES"]["MAIN_OUTPUT"]

        # Read data from both tables
        adjusted_df = db_writer.read(
            table_name=adjusted_table,
            filters=filters if filters else None,
            latest_only=True
        )

        if adjusted_df.empty:
            return JSONResponse(
                status_code=404,
                content={"status": "error", "message": "No data found in adjusted_data_hb with the specified filters"}
            )

        # Read performance data
        performance_df = db_writer.read(
            table_name=performance_table,
            latest_only=True
        )

        # Define join columns
        join_columns = ['GRP_NM', 'DPT_NM', 'CLSS_NM', 'SUB_CLSS_NM', 'LOC_CD']

        # Select required columns from adjusted_data_hb
        adjusted_columns = ['GRP_NM', 'DPT_NM', 'CLSS_NM', 'SUB_CLSS_NM', 'LOC_CD', 'Performance', 'GMV_PER_LM']
        available_adjusted_cols = [col for col in adjusted_columns if col in adjusted_df.columns]
        adjusted_subset = adjusted_df[available_adjusted_cols].copy()

        # Select required columns from overall_performance_data_hb
        performance_columns = [
            'GRP_NM', 'DPT_NM', 'CLSS_NM', 'SUB_CLSS_NM', 'LOC_CD',
            'total_lm_avg', 'UNITS_PER_INV', 'CUST_PEN', 'COVER', 'MARGIN_PERC',
            'ASP', 'perf_bucket', 'lm_bucket', 'action'
        ]

        if not performance_df.empty:
            available_perf_cols = [col for col in performance_columns if col in performance_df.columns]
            performance_subset = performance_df[available_perf_cols].copy()

            # Perform LEFT JOIN
            result_df = pd.merge(
                adjusted_subset,
                performance_subset,
                on=[col for col in join_columns if col in adjusted_subset.columns and col in performance_subset.columns],
                how='left'
            )
        else:
            # If performance table is empty, just use adjusted data
            result_df = adjusted_subset.copy()
            # Add missing performance columns as None
            missing_perf_cols = [col for col in performance_columns if col not in join_columns and col not in result_df.columns]
            for col in missing_perf_cols:
                result_df[col] = None

        # Create combined_bucket field
        if 'perf_bucket' in result_df.columns and 'lm_bucket' in result_df.columns:
            result_df['combined_bucket'] = (
                result_df['perf_bucket'].astype(str) + result_df['lm_bucket'].astype(str)
            ).replace('nannan', '-').replace('noneNone', '-').replace('None', '-').replace('nan', '-')
        else:
            result_df['combined_bucket'] = '-'

        # Replace NaN and None values with '-' for display
        result_df = result_df.fillna('-')
        result_df = result_df.replace('None', '-')
        result_df = result_df.replace('nan', '-')

        # Convert to list of dictionaries
        result_data = result_df.to_dict('records')

        return {
            "status": "success",
            "message": f"Retrieved {len(result_data)} performance records",
            "data": result_data,
            "total_records": len(result_data),
            "join_info": {
                "adjusted_records": len(adjusted_subset),
                "performance_records": len(performance_df) if not performance_df.empty else 0,
                "joined_records": len(result_df)
            }
        }

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": f"Failed to fetch performance data: {str(e)}"}
        )
