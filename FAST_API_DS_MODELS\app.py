from fastapi import <PERSON><PERSON><PERSON>, Body, HTTPException
from fastapi.responses import JSONResponse
import subprocess
import sys
from typing import Dict, Any, List, Optional
from pathlib import Path
from pydantic import BaseModel
from shared_config import get_config, update_config
from db_writer import DBWriter

base_dir = Path(__file__).resolve().parent
script_path = base_dir / "run_all_modules.py"
app = FastAPI()

@app.get("/")
def root():
    return {"message": "Welcome to the Optimizer API. Use /run-optimizer (POST) or /get-config (GET)."}

@app.get("/get-config")
async def fetch_config():
    try:
        return get_config()
    except Exception as e:
        return JSONResponse(status_code=500, content={"status": "error", "message": str(e)})

@app.post("/run-optimizer")
async def run_optimizer(overrides: Dict[str, Any] = Body(...)):
    try:
        if overrides:
            update_config(overrides)

        result = subprocess.run([sys.executable, script_path], capture_output=True, text=True)
        if result.returncode != 0:
            return JSONResponse(status_code=500, content={
                "status": "error",
                "message": "Pipeline execution failed",
                "stderr": result.stderr
            })

        return {
            "status": "success",
            "message": "Pipeline executed successfully",
            "stdout": result.stdout
        }

    except Exception as e:
        return JSONResponse(status_code=500, content={"status": "error", "message": str(e)})

@app.post("/run/{module_name}")
async def run_single_module(module_name: str, overrides: Dict[str, Any] = Body(default={})):
    try:
        valid_modules = ["datapreparation", "saturation", "optimization"]

        if module_name not in valid_modules:
            return JSONResponse(status_code=400, content={
                "status": "error",
                "message": f"Invalid module name. Choose from: {valid_modules}"
            })

        if overrides:
            update_config(overrides)

        result = subprocess.run(
            [sys.executable, "run_all_modules.py", module_name],
            capture_output=True,
            text=True
        )

        if result.returncode != 0:
            return JSONResponse(status_code=500, content={
                "status": "error",
                "message": f"{module_name} module failed.",
                "stderr": result.stderr
            })

        return {
            "status": "success",
            "message": f"{module_name} module executed successfully",
            "stdout": result.stdout
        }

    except Exception as e:
        return JSONResponse(status_code=500, content={"status": "error", "message": str(e)})
